generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "cockroachdb"
  url      = env("DATABASE_URL")
}

model Widget {
  id String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
}

model User {
  id               String   @id @default(uuid())
  username         String   @unique
  imageURL         String
  externalUserId   String   @unique
  bio              String?

  name             String?
  birthday         DateTime?
  location         String?
  gender           String?
  interests        String[] @default([])
  profileComplete  Boolean  @default(false)

   isVerified     Boolean  @default(false)

  following        Follow[] @relation("Following")
  followedBy       Follow[] @relation("FollowedBy")

  blocking         Block[] @relation("Blocking")
  blockedBy        Block[] @relation("BlockedBy")

  streams                        Stream[]
  streamParticipations           StreamParticipant[]
  sentInvitations                StreamInvitation[] @relation("SentInvitations")
  receivedInvitations            StreamInvitation[] @relation("ReceivedInvitations")
  hostedRecordedSessions         RecordedSession[] @relation("HostedSessions")
  recordedSessionParticipations  RecordedSessionParticipant[]
  
  hostedHangouts                 HangoutRoom[] @relation("HostedHangouts")
  hangoutParticipations          HangoutParticipant[]

  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

enum StreamRole {
  HOST
  CO_HOST
  GUEST
  VIEWER
}

enum ParticipantStatus {
  INVITED
  JOINED
  LEFT
  KICKED
  BANNED
}

model Stream {
  id                    String   @id @default(uuid())
  name                  String
  thumbnailUrl          String?
  ingressId             String?  @unique
  serverUrl             String?
  streamKey             String?

  isLive                Boolean  @default(false)
  isChatEnabled         Boolean  @default(true)
  isChatDelayed         Boolean  @default(false)
  isChatFollowersOnly   Boolean  @default(false)

  maxParticipants       Int      @default(8)
  allowGuests           Boolean  @default(true)
  requireApproval       Boolean  @default(false)

  userId                String
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  participants          StreamParticipant[]
  invitations           StreamInvitation[]
  recordedSessions      RecordedSession[]

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@index([userId])
  @@index([ingressId])
}

model StreamParticipant {
  id          String            @id @default(uuid())
  streamId    String
  userId      String
  role        StreamRole        @default(VIEWER)
  status      ParticipantStatus @default(JOINED)
  permissions Json?
  joinedAt    DateTime          @default(now())
  leftAt      DateTime?

  stream      Stream            @relation(fields: [streamId], references: [id], onDelete: Cascade)
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([streamId, userId])
  @@index([streamId])
  @@index([userId])
  @@index([status])
}

model StreamInvitation {
  id         String            @id @default(uuid())
  streamId   String
  inviterId  String
  inviteeId  String
  role       StreamRole        @default(GUEST)
  status     ParticipantStatus @default(INVITED)
  expiresAt  DateTime
  createdAt  DateTime          @default(now())

  stream     Stream            @relation(fields: [streamId], references: [id], onDelete: Cascade)
  inviter    User              @relation("SentInvitations", fields: [inviterId], references: [id], onDelete: Cascade)
  invitee    User              @relation("ReceivedInvitations", fields: [inviteeId], references: [id], onDelete: Cascade)

  @@unique([streamId, inviteeId])
  @@index([streamId])
  @@index([inviteeId])
  @@index([inviterId])
  @@index([status])
}

model Follow {
  id           String @id @default(uuid())
  followerId   String
  followingId  String

  follower     User   @relation(name: "Following", fields: [followerId], references: [id], onDelete: Cascade)
  following    User   @relation(name: "FollowedBy", fields: [followingId], references: [id], onDelete: Cascade)

  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([followerId, followingId])
  @@index([followerId])
  @@index([followingId])
}

model Block {
  id         String @id @default(uuid())
  blockerId  String
  blockedId  String

  blocked    User   @relation("BlockedBy", fields: [blockedId], references: [id], onDelete: Cascade)
  blocker    User   @relation("Blocking", fields: [blockerId], references: [id], onDelete: Cascade)

  @@unique([blockerId, blockedId])
  @@index([blockerId])
  @@index([blockedId])
}

enum RecordingStatus {
  PROCESSING
  READY
  FAILED
  PUBLISHED
}

enum HangoutStatus {
  ACTIVE
  ENDED
}

model HangoutRoom {
  id                    String          @id @default(uuid())
  name                  String
  hostUserId            String
  status                HangoutStatus   @default(ACTIVE)
  participantCount      Int             @default(1)
  maxParticipants       Int             @default(8)
  isPublic              Boolean         @default(true)
  requireApproval       Boolean         @default(false)
  
  host                  User            @relation("HostedHangouts", fields: [hostUserId], references: [id], onDelete: Cascade)
  participants          HangoutParticipant[]
  
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  endedAt               DateTime?

  @@index([hostUserId])
  @@index([status])
  @@index([isPublic])
}

model HangoutParticipant {
  id          String            @id @default(uuid())
  roomId      String
  userId      String
  role        StreamRole        @default(VIEWER)
  status      ParticipantStatus @default(JOINED)
  joinedAt    DateTime          @default(now())
  leftAt      DateTime?

  room        HangoutRoom       @relation(fields: [roomId], references: [id], onDelete: Cascade)
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([roomId, userId])
  @@index([roomId])
  @@index([userId])
  @@index([status])
}

model RecordedSession {
  id                     String   @id @default(uuid())
  streamId               String
  hostUserId             String
  title                  String
  description            String?
  egressId               String   @unique
  videoUrl               String?
  hlsUrl                 String?
  thumbnailUrl           String?
  duration               Int?
  participantCount       Int      @default(1)
  recordingStartedAt     DateTime
  recordingEndedAt       DateTime?
  processingStatus       RecordingStatus @default(PROCESSING)
  isPublished            Boolean  @default(false)
  publishedAt            DateTime?

  stream                 Stream   @relation(fields: [streamId], references: [id], onDelete: Cascade)
  host                   User     @relation("HostedSessions", fields: [hostUserId], references: [id], onDelete: Cascade)
  participants           RecordedSessionParticipant[]

  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  @@index([streamId])
  @@index([hostUserId])
  @@index([egressId])
  @@index([processingStatus])
  @@index([isPublished])
}

model RecordedSessionParticipant {
  id         String   @id @default(uuid())
  sessionId  String
  userId     String
  joinedAt   DateTime
  leftAt     DateTime?

  session    RecordedSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user       User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([sessionId, userId])
  @@index([sessionId])
  @@index([userId])
}
