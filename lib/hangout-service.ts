import { db } from "@/lib/db";
import { HangoutStatus, ParticipantStatus, StreamRole } from "@prisma/client";

export const createHangoutRoom = async (hostUserId: string, name: string) => {
    try {
        const room = await db.hangoutRoom.create({
            data: {
                name,
                hostUserId,
                participants: {
                    create: {
                        userId: hostUserId,
                        role: StreamRole.HOST,
                        status: ParticipantStatus.JOINED,
                    }
                }
            },
            include: {
                host: true,
                participants: {
                    include: {
                        user: true,
                    }
                }
            }
        });

        console.log(`[HANGOUT_CREATED] Room ${room.id} created by ${room.host.username}`);
        return room;
    } catch (error) {
        console.error("[HANGOUT_CREATE_ERROR]", error);
        throw error;
    }
};

export const getActiveHangoutRooms = async () => {
    try {
        const rooms = await db.hangoutRoom.findMany({
            where: {
                status: HangoutStatus.ACTIVE,
            },
            include: {
                host: true,
                participants: {
                    where: {
                        status: ParticipantStatus.JOINED,
                    },
                    include: {
                        user: true,
                    }
                }
            },
            orderBy: {
                createdAt: 'desc',
            }
        });

        return rooms;
    } catch (error) {
        console.error("[GET_ACTIVE_HANGOUTS_ERROR]", error);
        throw error;
    }
};

export const joinHangoutRoom = async (roomId: string, userId: string) => {
    try {
        // Check if room exists and is active
        const room = await db.hangoutRoom.findFirst({
            where: {
                id: roomId,
                status: HangoutStatus.ACTIVE,
            },
            include: {
                participants: {
                    where: {
                        status: ParticipantStatus.JOINED,
                    }
                }
            }
        });

        if (!room) {
            throw new Error("Room not found or not active");
        }

        if (room.participants.length >= room.maxParticipants) {
            throw new Error("Room is full");
        }

        // Check if user is already in the room
        const existingParticipant = await db.hangoutParticipant.findFirst({
            where: {
                roomId,
                userId,
                status: ParticipantStatus.JOINED,
            }
        });

        if (existingParticipant) {
            return existingParticipant;
        }

        // Add user to room
        const participant = await db.hangoutParticipant.create({
            data: {
                roomId,
                userId,
                status: ParticipantStatus.JOINED,
            },
            include: {
                user: true,
                room: true,
            }
        });

        // Update participant count
        await db.hangoutRoom.update({
            where: { id: roomId },
            data: {
                participantCount: {
                    increment: 1,
                }
            }
        });

        console.log(`[HANGOUT_JOINED] User ${participant.user.username} joined room ${roomId}`);
        return participant;
    } catch (error) {
        console.error("[HANGOUT_JOIN_ERROR]", error);
        throw error;
    }
};

export const leaveHangoutRoom = async (roomId: string, userId: string) => {
    try {
        const participant = await db.hangoutParticipant.findFirst({
            where: {
                roomId,
                userId,
                status: ParticipantStatus.JOINED,
            }
        });

        if (!participant) {
            return null;
        }

        await db.hangoutParticipant.update({
            where: { id: participant.id },
            data: {
                status: ParticipantStatus.LEFT,
                leftAt: new Date(),
            }
        });

        // Update participant count
        await db.hangoutRoom.update({
            where: { id: roomId },
            data: {
                participantCount: {
                    decrement: 1,
                }
            }
        });

        // Check if host left, end the room
        const room = await db.hangoutRoom.findUnique({
            where: { id: roomId },
            include: {
                participants: {
                    where: {
                        status: ParticipantStatus.JOINED,
                    }
                }
            }
        });

        if (room && room.hostUserId === userId) {
            await endHangoutRoom(roomId);
        }

        console.log(`[HANGOUT_LEFT] User ${userId} left room ${roomId}`);
        return participant;
    } catch (error) {
        console.error("[HANGOUT_LEAVE_ERROR]", error);
        throw error;
    }
};

export const endHangoutRoom = async (roomId: string) => {
    try {
        const room = await db.hangoutRoom.update({
            where: { id: roomId },
            data: {
                status: HangoutStatus.ENDED,
                endedAt: new Date(),
            }
        });

        // Mark all participants as left
        await db.hangoutParticipant.updateMany({
            where: {
                roomId,
                status: ParticipantStatus.JOINED,
            },
            data: {
                status: ParticipantStatus.LEFT,
                leftAt: new Date(),
            }
        });

        console.log(`[HANGOUT_ENDED] Room ${roomId} ended`);
        return room;
    } catch (error) {
        console.error("[HANGOUT_END_ERROR]", error);
        throw error;
    }
};

export const getHangoutRoom = async (roomId: string) => {
    try {
        const room = await db.hangoutRoom.findUnique({
            where: { id: roomId },
            include: {
                host: true,
                participants: {
                    where: {
                        status: ParticipantStatus.JOINED,
                    },
                    include: {
                        user: true,
                    }
                }
            }
        });

        return room;
    } catch (error) {
        console.error("[GET_HANGOUT_ROOM_ERROR]", error);
        throw error;
    }
};

export const getUserActiveHangoutRooms = async (userId: string) => {
    try {
        const rooms = await db.hangoutRoom.findMany({
            where: {
                status: HangoutStatus.ACTIVE,
                participants: {
                    some: {
                        userId,
                        status: ParticipantStatus.JOINED,
                    }
                }
            },
            include: {
                host: true,
                participants: {
                    where: {
                        status: ParticipantStatus.JOINED,
                    },
                    include: {
                        user: true,
                    }
                }
            },
            orderBy: {
                createdAt: 'desc',
            }
        });

        return rooms;
    } catch (error) {
        console.error("[GET_USER_ACTIVE_HANGOUTS_ERROR]", error);
        throw error;
    }
}; 