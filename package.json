{"name": "building-popcircle", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "vercel-build": "prisma generate && next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format:check": "prettier --check \"**/*.{ts,tsx,md,json}\"", "format:write": "prettier --write \"**/*.{ts,tsx,md,json}\""}, "dependencies": {"@clerk/clerk-sdk-node": "^5.1.6", "@clerk/nextjs": "^6.12.8", "@clerk/themes": "^2.2.20", "@livekit/components-core": "^0.12.2", "@livekit/components-react": "^2.9.10", "@livekit/components-styles": "1.1.6", "@livekit/krisp-noise-filter": "^0.2.16", "@livekit/track-processors": "^0.5.4", "@prisma/client": "^6.9.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "aws4fetch": "^1.0.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "livekit-client": "2.13.3", "livekit-server-sdk": "2.13.0", "lucide-react": "^0.479.0", "next": "^15.0.0", "next-themes": "^0.4.6", "query-string": "^9.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "sonner": "^2.0.1", "svix": "^1.61.4", "tailwind-merge": "^3.0.2", "tailwind-scrollbar-hide": "^4.0.0", "tailwindcss-animate": "^1.0.7", "tinykeys": "^3.0.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "assert": "^2.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "eslint": "^9", "eslint-config-next": "15.2.1", "prisma": "^6.9.0", "process": "^0.11.10", "stream-browserify": "^3.0.0", "tailwindcss": "^4", "typescript": "^5", "util": "^0.12.5"}}