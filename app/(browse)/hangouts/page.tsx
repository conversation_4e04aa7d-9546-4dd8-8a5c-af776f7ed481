import { HangoutLanding } from "@/components/hangout-landing";
import { currentUser } from "@clerk/nextjs/server";
import { getActiveHangoutRooms } from "@/lib/hangout-service";
import { db } from "@/lib/db";

export default async function HangoutsPage() {
    const external = await currentUser();
    let current = null;
    if (external) {
        current = await db.user.findUnique({ where: { externalUserId: external.id } });
    }

    const activeHangouts = await getActiveHangoutRooms();

    return (
        <HangoutLanding
            activeHangouts={activeHangouts}
            currentUser={current}
        />
    );
} 