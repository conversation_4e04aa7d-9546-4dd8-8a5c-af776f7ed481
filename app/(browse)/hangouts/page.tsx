import { HangoutLanding } from "@/components/hangout-landing";
import { currentUser } from "@clerk/nextjs/server";
import { getActiveHangoutRooms } from "@/lib/hangout-service";
import { db } from "@/lib/db";

export default async function HangoutsPage() {
    const external = await currentUser();
    let current = null;
    if (external) {
        current = await db.user.findUnique({ where: { externalUserId: external.id } });
    }

    const activeHangouts = await getActiveHangoutRooms();

    return (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        <HangoutLanding
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            activeHangouts={activeHangouts as any}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            currentUser={current as any}
        />
    );
} 