import { Suspense } from "react";
import { notFound } from "next/navigation";
import { HangoutRoom } from "@/components/hangout-room";
import { currentUser } from "@clerk/nextjs/server";

export default async function HangoutPage({
    params,
}: {
    params: Promise<{ hangoutId: string }>;
}) {
    const { hangoutId } = await params;
    const externalUser = await currentUser();

    if (!externalUser) {
        notFound();
    }

    // For now, mock hangout data until database is set up
    const mockHangout = {
        id: hangoutId,
        name: "<PERSON>'s hangout",
        host: {
            id: "user-1",
            username: "<PERSON>",
            imageURL: "/placeholder-avatar.png",
            externalUserId: "external-1",
            bio: null,
            name: null,
            birthday: null,
            location: null,
            gender: null,
            interests: [],
            profileComplete: true,
            isVerified: false,
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        participants: [
            {
                id: "participant-1",
                user: {
                    id: "user-1",
                    username: "<PERSON>",
                    imageURL: "/placeholder-avatar.png",
                    externalUserId: "external-1",
                    bio: null,
                    name: null,
                    birthday: null,
                    location: null,
                    gender: null,
                    interests: [],
                    profileComplete: true,
                    isVerified: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                isAudioEnabled: true,
                isVideoEnabled: true,
                isSpeaking: false,
                isHost: true,
            },
            {
                id: "participant-2",
                user: {
                    id: "user-2",
                    username: "Sarah",
                    imageURL: "/placeholder-avatar.png",
                    externalUserId: "external-2",
                    bio: null,
                    name: null,
                    birthday: null,
                    location: null,
                    gender: null,
                    interests: [],
                    profileComplete: true,
                    isVerified: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                isAudioEnabled: true,
                isVideoEnabled: false,
                isSpeaking: true,
                isHost: false,
            },
            {
                id: "participant-3",
                user: {
                    id: "user-3",
                    username: "James",
                    imageURL: "/placeholder-avatar.png",
                    externalUserId: "external-3",
                    bio: null,
                    name: null,
                    birthday: null,
                    location: null,
                    gender: null,
                    interests: [],
                    profileComplete: true,
                    isVerified: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                isAudioEnabled: false,
                isVideoEnabled: true,
                isSpeaking: false,
                isHost: false,
            },
        ],
    };

    const currentUserData = {
        id: externalUser.id,
        username: externalUser.username || 'user',
        imageURL: externalUser.imageUrl || '',
        externalUserId: externalUser.id,
        bio: null,
        name: externalUser.firstName || null,
        birthday: null,
        location: null,
        gender: null,
        interests: [],
        profileComplete: true,
        isVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };

    return (
        <div className="h-screen">
            <Suspense fallback={<div>Loading hangout...</div>}>
                <HangoutRoom
                    roomName={mockHangout.name}
                    host={mockHangout.host}
                    currentUser={currentUserData}
                    participants={mockHangout.participants}
                />
            </Suspense>
        </div>
    );
} 