import { Suspense } from "react";
import { notFound } from "next/navigation";
import { HangoutRoom } from "@/components/hangout-room";
import { currentUser } from "@clerk/nextjs/server";
import { getHangoutRoom } from "@/lib/hangout-service";
import { db } from "@/lib/db";

export default async function HangoutPage({
    params,
}: {
    params: Promise<{ hangoutId: string }>;
}) {
    const { hangoutId } = await params;
    const externalUser = await currentUser();

    if (!externalUser) {
        notFound();
    }

    // Get current user from database
    const currentUserData = await db.user.findUnique({
        where: { externalUserId: externalUser.id }
    });

    if (!currentUserData) {
        notFound();
    }

    // Fetch the actual hangout room from database
    const hangoutRoom = await getHangoutRoom(hangoutId);

    if (!hangoutRoom) {
        notFound();
    }

    // Transform database participants to component format
    const participants = hangoutRoom.participants.map(participant => ({
        id: participant.id,
        user: participant.user,
        isAudioEnabled: true, // Default values - these would come from real-time data
        isVideoEnabled: true,
        isSpeaking: false,
        isHost: participant.userId === hangoutRoom.hostUserId,
    }));

    return (
        <div className="h-screen">
            <Suspense fallback={<div>Loading hangout...</div>}>
                <HangoutRoom
                    roomName={hangoutRoom.name}
                    host={hangoutRoom.host}
                    currentUser={currentUserData}
                    hangoutId={hangoutId}
                />
            </Suspense>
        </div>
    );
} 