"use client";

import Link from "next/link";
import { useSidebar } from "@/store/use-sidebar";
import { UserAvatar } from "@/components/user-avatar";
import { Skeleton } from "@/components/ui/skeleton";

interface UserItemProps {
  username: string;
  imageUrl: string;
}

export const UserItem = ({ username, imageUrl }: UserItemProps) => {
  const { collapsed } = useSidebar((state) => state);

  const href = `/${username}`;

  return (
    <div className="group flex items-center w-full p-2 rounded-md hover:bg-accent/5">
      <Link href={href} className="flex items-center w-full gap-x-4">
        <UserAvatar
          imageUrl={imageUrl}
          username={username}
          showBadge
        />
        {!collapsed && (
          <p className="truncate">{username}</p>
        )}
      </Link>
    </div>
  );
};

export const UserItemSkeleton = () => {
  return (
    <div className="flex items-center gap-x-4 px-3 py-2">
      <Skeleton className="min-h-[32px] min-w-[32px] rounded-full" />
      <div className="flex-1">
        <Skeleton className="h-6" />
      </div>
    </div>
  );
};
