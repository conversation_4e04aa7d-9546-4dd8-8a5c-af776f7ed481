import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { getUserById } from "@/lib/user-service";
import {
  EgressClient,
  EncodedFileOutput,
  SegmentedFileOutput,
  EncodingOptionsPreset,
  EncodedOutputs,
} from "livekit-server-sdk";

const egressClient = new EgressClient(
  process.env.LIVEKIT_API_URL!,
  process.env.LIVEKIT_API_KEY!,
  process.env.LIVEKIT_API_SECRET!
);

export async function POST(req: NextRequest) {
  try {
    const { roomName, title, description } = await req.json();

<<<<<<< HEAD
        if (!roomName) {
            return NextResponse.json({ error: "Room name is required" }, { status: 400 });
        }

        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get host information - roomName could be database ID or external ID
        let host = await getUserById(roomName);

        // If not found by database ID, try by external ID
        if (!host) {
            host = await db.user.findUnique({
                where: { externalUserId: roomName },
                include: {
                    streams: {
                        take: 1,
                        orderBy: { createdAt: "desc" },
                    },
                },
            });
        }

        if (!host) {
            return NextResponse.json({ error: "Host not found" }, { status: 404 });
        }

        // Verify the authenticated user is the host
        if (host.externalUserId !== userId) {
            return NextResponse.json({ error: "Unauthorized - not the host" }, { status: 403 });
        }

        // Get the user's stream
        const userStream = await db.stream.findFirst({
            where: { userId: host.id }
        });

        if (!userStream) {
            return NextResponse.json({ error: "Stream not found" }, { status: 404 });
        }

        // Generate unique filename for this recording with proper folder structure
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}`;
        const folderPath = `recordings/${host.username}`;
        const fullPath = `${folderPath}/${filename}`;

        console.log(`[RECORDING_START_DEBUG] R2 Configuration:`);
        console.log(`[RECORDING_START_DEBUG] Endpoint: ${process.env.CLOUDFLARE_R2_ENDPOINT}`);
        console.log(`[RECORDING_START_DEBUG] Bucket: ${process.env.CLOUDFLARE_R2_BUCKET}`);
        console.log(`[RECORDING_START_DEBUG] Access Key: ${process.env.CLOUDFLARE_R2_ACCESS_KEY ? '***SET***' : 'NOT SET'}`);
        console.log(`[RECORDING_START_DEBUG] Secret Key: ${process.env.CLOUDFLARE_R2_SECRET_KEY ? '***SET***' : 'NOT SET'}`);
        console.log(`[RECORDING_START_DEBUG] Filename: ${filename}`);
        console.log(`[RECORDING_START_DEBUG] Folder Path: ${folderPath}`);
        console.log(`[RECORDING_START_DEBUG] Full Path: ${fullPath}`);
        console.log(`[RECORDING_START_DEBUG] Host: ${host.username}`);

        // Configure outputs for Cloudflare R2
        const outputs: EncodedOutputs = {
            // File output for MP4 recording
            file: new EncodedFileOutput({
                filepath: `${fullPath}.mp4`,
                output: {
                    case: 's3',
                    value: {
                        endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
                        bucket: process.env.CLOUDFLARE_R2_BUCKET!,
                        accessKey: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
                        secret: process.env.CLOUDFLARE_R2_SECRET_KEY!,
                        region: 'auto',
                        forcePathStyle: true,
                    },
                },
            }),
            // Segmented output for HLS streaming
            segments: new SegmentedFileOutput({
                filenamePrefix: fullPath,
                playlistName: `${fullPath}.m3u8`,
                livePlaylistName: `${fullPath}-live.m3u8`,
                segmentDuration: 6,
                output: {
                    case: 's3',
                    value: {
                        endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
                        bucket: process.env.CLOUDFLARE_R2_BUCKET!,
                        accessKey: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
                        secret: process.env.CLOUDFLARE_R2_SECRET_KEY!,
                        region: 'auto',
                        forcePathStyle: true,
                    },
                },
            }),
        };

        // Start room composite egress with custom vertical recording template
        const customBaseUrl = process.env.NODE_ENV === 'production'
            ? `https://${process.env.VERCEL_URL || 'building-popcircle.vercel.app'}/recording-template`
            : `http://localhost:3000/recording-template`;

        const egressInfo = await egressClient.startRoomCompositeEgress(
            "global-dashboard-room", // Use the actual LiveKit room name
            outputs,
            {
                layout: 'vertical-split', // Custom layout identifier
                encodingOptions: EncodingOptionsPreset.PORTRAIT_H264_720P_30, // Use portrait preset for 9:16
                audioOnly: false,
                customBaseUrl: customBaseUrl
            }
        );

        // Create database record for the recording session
        const recordedSession = await db.recordedSession.create({
            data: {
                streamId: userStream.id,
                hostUserId: host.id,
                title: title || `${host.username}'s Session`,
                description: description || null,
                egressId: egressInfo.egressId,
                videoUrl: `${fullPath}.mp4`,
                hlsUrl: `${fullPath}.m3u8`,
                recordingStartedAt: new Date(),
                processingStatus: 'PROCESSING',
            },
        });

        console.log(`[RECORDING_START] Started recording for room ${roomName}, egress ID: ${egressInfo.egressId}`);

        return NextResponse.json({
            success: true,
            egressId: egressInfo.egressId,
            sessionId: recordedSession.id,
            message: "Recording started successfully"
        });

    } catch (error) {
        console.error("Failed to start recording:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to start recording" },
            { status: 500 }
        );
=======
    if (!roomName) {
      return NextResponse.json(
        { error: "Room name is required" },
        { status: 400 }
      );
>>>>>>> origin/main
    }

    // Get authenticated user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get host information
    const host = await getUserById(roomName);
    if (!host || host.externalUserId !== userId) {
      return NextResponse.json(
        { error: "Unauthorized or host not found" },
        { status: 403 }
      );
    }

    // Get the user's stream
    const userStream = await db.stream.findFirst({
      where: { userId: host.id },
    });

    if (!userStream) {
      return NextResponse.json({ error: "Stream not found" }, { status: 404 });
    }

    // Generate unique filename for this recording with proper folder structure
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `${timestamp}`;
    const folderPath = `recordings/${host.username}`;
    const fullPath = `${folderPath}/${filename}`;

    console.log(`[RECORDING_START_DEBUG] R2 Configuration:`);
    console.log(
      `[RECORDING_START_DEBUG] Endpoint: ${process.env.CLOUDFLARE_R2_ENDPOINT}`
    );
    console.log(
      `[RECORDING_START_DEBUG] Bucket: ${process.env.CLOUDFLARE_R2_BUCKET}`
    );
    console.log(
      `[RECORDING_START_DEBUG] Access Key: ${
        process.env.CLOUDFLARE_R2_ACCESS_KEY ? "***SET***" : "NOT SET"
      }`
    );
    console.log(
      `[RECORDING_START_DEBUG] Secret Key: ${
        process.env.CLOUDFLARE_R2_SECRET_KEY ? "***SET***" : "NOT SET"
      }`
    );
    console.log(`[RECORDING_START_DEBUG] Filename: ${filename}`);
    console.log(`[RECORDING_START_DEBUG] Folder Path: ${folderPath}`);
    console.log(`[RECORDING_START_DEBUG] Full Path: ${fullPath}`);
    console.log(`[RECORDING_START_DEBUG] Host: ${host.username}`);

    // Configure outputs for Cloudflare R2
    const outputs: EncodedOutputs = {
      // File output for MP4 recording
      file: new EncodedFileOutput({
        filepath: `${fullPath}.mp4`,
        output: {
          case: "s3",
          value: {
            endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
            bucket: process.env.CLOUDFLARE_R2_BUCKET!,
            accessKey: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
            secret: process.env.CLOUDFLARE_R2_SECRET_KEY!,
            region: "auto",
            forcePathStyle: true,
          },
        },
      }),
      // Segmented output for HLS streaming
      segments: new SegmentedFileOutput({
        filenamePrefix: fullPath,
        playlistName: `${fullPath}.m3u8`,
        livePlaylistName: `${fullPath}-live.m3u8`,
        segmentDuration: 6,
        output: {
          case: "s3",
          value: {
            endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
            bucket: process.env.CLOUDFLARE_R2_BUCKET!,
            accessKey: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
            secret: process.env.CLOUDFLARE_R2_SECRET_KEY!,
            region: "auto",
            forcePathStyle: true,
          },
        },
      }),
    };

    // Start room composite egress with active speaker layout
    const egressInfo = await egressClient.startRoomCompositeEgress(
      roomName,
      outputs,
      "speaker",
      EncodingOptionsPreset.H264_720P_30,
      false // audioOnly
    );

    // Create database record for the recording session
    const recordedSession = await db.recordedSession.create({
      data: {
        streamId: userStream.id,
        hostUserId: host.id,
        title: title || `${host.username}'s Session`,
        description: description || null,
        egressId: egressInfo.egressId,
        videoUrl: `${fullPath}.mp4`,
        hlsUrl: `${fullPath}.m3u8`,
        recordingStartedAt: new Date(),
        processingStatus: "PROCESSING",
      },
    });

    console.log(
      `[RECORDING_START] Started recording for room ${roomName}, egress ID: ${egressInfo.egressId}`
    );

    return NextResponse.json({
      success: true,
      egressId: egressInfo.egressId,
      sessionId: recordedSession.id,
      message: "Recording started successfully",
    });
  } catch (error) {
    console.error("Failed to start recording:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to start recording",
      },
      { status: 500 }
    );
  }
}
