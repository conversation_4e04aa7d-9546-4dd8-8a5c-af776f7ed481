import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { AccessToken } from "livekit-server-sdk";
import { db } from "@/lib/db";
import { StreamRole } from "@prisma/client";

export async function POST(req: NextRequest) {
  try {
    const { hangoutId } = await req.json();

    if (!hangoutId) {
      return NextResponse.json(
        { error: "Hangout ID is required" },
        { status: 400 }
      );
    }

    // Get authenticated user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database
    const user = await db.user.findUnique({
      where: { externalUserId: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get hangout room
    const hangoutRoom = await db.hangoutRoom.findUnique({
      where: { id: hangoutId },
      include: {
        host: true,
        participants: {
          where: { userId: user.id },
        },
      },
    });

    if (!hangoutRoom) {
      return NextResponse.json(
        { error: "Hangout room not found" },
        { status: 404 }
      );
    }

    // Check if user is host or participant
    const isHost = hangoutRoom.hostUserId === user.id;
    const isParticipant = hangoutRoom.participants.length > 0;

    if (!isHost && !isParticipant) {
      // Auto-join user as participant if room is public
      if (hangoutRoom.isPublic) {
        await db.hangoutParticipant.create({
          data: {
            roomId: hangoutId,
            userId: user.id,
            role: StreamRole.VIEWER,
            status: "JOINED",
          },
        });
      } else {
        return NextResponse.json(
          { error: "Not authorized to join this hangout" },
          { status: 403 }
        );
      }
    }

    // Determine role and permissions
    const role = isHost ? StreamRole.HOST : StreamRole.VIEWER;
    const permissions = {
      canPublish: true, // All participants can publish audio/video
      canPublishData: true,
      canPublishScreen: isHost, // Only host can screen share for now
      canSubscribe: true,
      canUpdateMetadata: isHost,
    };

    // Create LiveKit token
    const at = new AccessToken(
      process.env.LIVEKIT_API_KEY!,
      process.env.LIVEKIT_API_SECRET!,
      {
        identity: user.externalUserId,
        name: user.username,
        metadata: JSON.stringify({ role, permissions }),
      }
    );

    // Use hangout ID as room name for LiveKit
    at.addGrant({
      room: `hangout-${hangoutId}`,
      roomJoin: true,
      canPublish: permissions.canPublish,
      canPublishData: permissions.canPublishData,
      canSubscribe: permissions.canSubscribe,
      canUpdateOwnMetadata: permissions.canUpdateMetadata,
    });

    const token = await at.toJwt();

    console.log(`[HANGOUT_TOKEN] Generated token for user ${user.username} in hangout ${hangoutId}`);

    return NextResponse.json({
      token,
      name: user.username,
      identity: user.externalUserId,
      role,
      permissions,
    });
  } catch (error) {
    console.error("Failed to create hangout token:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to create hangout token",
      },
      { status: 500 }
    );
  }
}
