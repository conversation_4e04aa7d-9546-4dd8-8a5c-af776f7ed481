/* Import Google Fonts for PopCircle Theme */
@import url("https://fonts.googleapis.com/css2?family=SansSerif:wght@200;300;400;500;600;700;800@display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900@display=swap");

/* Tailwind CSS directives */
@tailwindcss /preflight;
@tailwind utilities;

@import "tailwindcss";
@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

/* PopCircle Theme Variables */
:root {
  --background: oklch(0.223 0.004 275);

  --foreground: oklch(0.2393 0 0);
  --card: oklch(0.7572 0 0);
  --card-foreground: oklch(0.2393 0 0);
  --popover: oklch(0.7572 0 0);
  --popover-foreground: oklch(0.2393 0 0);
  --primary: oklch(0.5016 0.1887 27.4816);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.4955 0.0896 126.1858);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.7826 0 0);
  --muted-foreground: oklch(0.4091 0 0);
  --accent: oklch(0.588 0.0993 245.7394);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.7076 0.1975 46.4558);
  --destructive-foreground: oklch(0 0 0);
  --border: oklch(0.4313 0 0);
  --input: oklch(0.4313 0 0);
  --ring: oklch(0.5016 0.1887 27.4816);
  --chart-1: oklch(0.5016 0.1887 27.4816);
  --chart-2: oklch(0.4955 0.0896 126.1858);
  --chart-3: oklch(0.588 0.0993 245.7394);
  --chart-4: oklch(0.7076 0.1975 46.4558);
  --chart-5: oklch(0.5656 0.0431 40.4319);
  --sidebar: oklch(0.7572 0 0);
  --sidebar-foreground: oklch(0.2393 0 0);
  --sidebar-primary: oklch(0.5016 0.1887 27.4816);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.588 0.0993 245.7394);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.4313 0 0);
  --sidebar-ring: oklch(0.5016 0.1887 27.4816);
  --font-sans: "SansSerif", sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: "Source Code Pro", monospace;
  --radius: 0px;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.2);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.2);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.4),
    0px 1px 2px -1px hsl(0 0% 0% / 0.4);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.4),
    0px 1px 2px -1px hsl(0 0% 0% / 0.4);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.4),
    0px 2px 4px -1px hsl(0 0% 0% / 0.4);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.4),
    0px 4px 6px -1px hsl(0 0% 0% / 0.4);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.4),
    0px 8px 10px -1px hsl(0 0% 0% / 0.4);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 1);

  /* LiveKit Theme Variables for Light Mode */
  --lk-bg: var(--background);
  --lk-fg: var(--foreground);
  --lk-accent: var(--primary);
  --lk-accent-fg: var(--primary-foreground);
  --lk-focus: var(--ring);
  --lk-border: var(--border);
  --lk-radius: var(--radius);
  --lk-font-family: var(--font-sans);
  --lk-font-family-mono: var(--font-mono);
  --lk-button-bg: var(--primary);
  --lk-button-fg: var(--primary-foreground);
  --lk-button-bg-hover: var(--accent);
  --lk-button-fg-hover: var(--accent-foreground);
  --lk-secondary: var(--secondary);
  --lk-secondary-fg: var(--secondary-foreground);
  --lk-muted: var(--muted);
  --lk-muted-fg: var(--muted-foreground);
  --lk-destructive: var(--destructive);
  --lk-destructive-fg: var(--destructive-foreground);
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.4;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.285 0 0);

  --foreground: oklch(0.9067 0 0);
  --card: oklch(0.285 0 0);
  --card-foreground: oklch(0.9067 0 0);
  --popover: oklch(0.285 0 0);
  --popover-foreground: oklch(0.9067 0 0);
  --primary: oklch(0.6083 0.209 27.0276);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.6423 0.1467 133.0145);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.2645 0 0);
  --muted-foreground: oklch(0.7058 0 0);
  --accent: oklch(0.7482 0.1235 244.7492);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.7839 0.1719 68.0943);
  --destructive-foreground: oklch(0 0 0);
  --border: oklch(0.4091 0 0);
  --input: oklch(0.4091 0 0);
  --ring: oklch(0.6083 0.209 27.0276);
  --chart-1: oklch(0.6083 0.209 27.0276);
  --chart-2: oklch(0.6423 0.1467 133.0145);
  --chart-3: oklch(0.7482 0.1235 244.7492);
  --chart-4: oklch(0.7839 0.1719 68.0943);
  --chart-5: oklch(0.6471 0.0334 40.7963);
  --sidebar: oklch(0.1913 0 0);
  --sidebar-foreground: oklch(0.9067 0 0);
  --sidebar-primary: oklch(0.6083 0.209 27.0276);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.7482 0.1235 244.7492);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.4091 0 0);
  --sidebar-ring: oklch(0.6083 0.209 27.0276);
  --font-sans: "SansSerif", sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: "Source Code Pro", monospace;
  --radius: 0px;
  --shadow-2xs: 0px 2px 5px 0px hsl(0 0% 0% / 0.3);
  --shadow-xs: 0px 2px 5px 0px hsl(0 0% 0% / 0.3);
  --shadow-sm: 0px 2px 5px 0px hsl(0 0% 0% / 0.6),
    0px 1px 2px -1px hsl(0 0% 0% / 0.6);
  --shadow: 0px 2px 5px 0px hsl(0 0% 0% / 0.6),
    0px 1px 2px -1px hsl(0 0% 0% / 0.6);
  --shadow-md: 0px 2px 5px 0px hsl(0 0% 0% / 0.6),
    0px 2px 4px -1px hsl(0 0% 0% / 0.6);
  --shadow-lg: 0px 2px 5px 0px hsl(0 0% 0% / 0.6),
    0px 4px 6px -1px hsl(0 0% 0% / 0.6);
  --shadow-xl: 0px 2px 5px 0px hsl(0 0% 0% / 0.6),
    0px 8px 10px -1px hsl(0 0% 0% / 0.6);
  --shadow-2xl: 0px 2px 5px 0px hsl(0 0% 0% / 1.5);

  /* LiveKit Theme Variables for Dark Mode */
  --lk-bg: var(--background);
  --lk-fg: var(--foreground);
  --lk-accent: var(--primary);
  --lk-accent-fg: var(--primary-foreground);
  --lk-focus: var(--ring);
  --lk-border: var(--border);
  --lk-radius: var(--radius);
  --lk-font-family: var(--font-sans);
  --lk-font-family-mono: var(--font-mono);
  --lk-button-bg: var(--primary);
  --lk-button-fg: var(--primary-foreground);
  --lk-button-bg-hover: var(--accent);
  --lk-button-fg-hover: var(--accent-foreground);
  --lk-secondary: var(--secondary);
  --lk-secondary-fg: var(--secondary-foreground);
  --lk-muted: var(--muted);
  --lk-muted-fg: var(--muted-foreground);
  --lk-destructive: var(--destructive);
  --lk-destructive-fg: var(--destructive-foreground);
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.6;
  --shadow-blur: 5px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: "SansSerif", sans-serif;
  --font-mono: "Source Code Pro", monospace;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --radius: 0px;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    font-family: var(--font-sans), "SansSerif", sans-serif;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans), "SansSerif", sans-serif;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
    scroll-behavior: smooth;
    letter-spacing: var(--tracking-normal);
  }

  /* Apply PopCircle fonts globally */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-sans), "SansSerif", sans-serif;
    font-weight: 600;
  }

  code,
  pre,
  .font-mono {
    font-family: var(--font-mono), "Source Code Pro", monospace;
  }
}

/* PopCircle Theme for LiveKit Components */
@layer components {
  /* Base LiveKit theming using PopCircle colors */
  [data-lk-theme="default"] {
    --lk-bg: var(--background);
    --lk-fg: var(--foreground);
    --lk-accent: var(--primary);
    --lk-accent-fg: var(--primary-foreground);
    --lk-focus: var(--ring);
    --lk-border: var(--border);
    --lk-radius: var(--radius);
    --lk-font-family: var(--font-sans);
    --lk-font-family-mono: var(--font-mono);

    /* Button styles */
    --lk-button-bg: var(--primary);
    --lk-button-fg: var(--primary-foreground);
    --lk-button-bg-hover: var(--accent);
    --lk-button-fg-hover: var(--accent-foreground);
    --lk-button-disabled-bg: var(--muted);
    --lk-button-disabled-fg: var(--muted-foreground);

    /* Control bar styles */
    --lk-control-bar-bg: var(--card);
    --lk-control-bar-fg: var(--card-foreground);
    --lk-control-bar-border: var(--border);

    /* Chat styles */
    --lk-chat-bg: var(--card);
    --lk-chat-fg: var(--card-foreground);
    --lk-chat-message-bg: var(--muted);
    --lk-chat-message-fg: var(--muted-foreground);

    /* Input styles */
    --lk-input-bg: var(--input);
    --lk-input-fg: var(--foreground);
    --lk-input-border: var(--border);
    --lk-input-focus-border: var(--ring);

    /* Secondary colors */
    --lk-secondary: var(--secondary);
    --lk-secondary-fg: var(--secondary-foreground);
    --lk-muted: var(--muted);
    --lk-muted-fg: var(--muted-foreground);
    --lk-destructive: var(--destructive);
    --lk-destructive-fg: var(--destructive-foreground);

    /* Shadows */
    --lk-shadow: var(--shadow);
    --lk-shadow-md: var(--shadow-md);
    --lk-shadow-lg: var(--shadow-lg);
  }

  /* Override LiveKit specific component styles with PopCircle theme */
  .lk-video-conference {
    font-family: var(--font-sans) !important;
    background: var(--background) !important;
    color: var(--foreground) !important;
  }

  .lk-participant-tile {
    border-radius: var(--radius) !important;
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
    box-shadow: var(--shadow) !important;
  }

  .lk-participant-name {
    font-family: var(--font-sans) !important;
    font-weight: 500 !important;
    color: var(--foreground) !important;
  }

  .lk-control-bar {
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-lg) !important;
    backdrop-filter: blur(8px);
  }

  .lk-button {
    background: var(--primary) !important;
    color: var(--primary-foreground) !important;
    border-radius: var(--radius) !important;
    font-family: var(--font-sans) !important;
    font-weight: 500 !important;
    border: none !important;
    box-shadow: var(--shadow) !important;
    transition: all 150ms ease !important;
  }

  .lk-button:hover {
    background: var(--accent) !important;
    color: var(--accent-foreground) !important;
    box-shadow: var(--shadow-md) !important;
    transform: translateY(-1px);
  }

  .lk-button:disabled {
    background: var(--muted) !important;
    color: var(--muted-foreground) !important;
    box-shadow: none !important;
    transform: none !important;
  }

  .lk-button.lk-button-danger {
    background: var(--destructive) !important;
    color: var(--destructive-foreground) !important;
  }

  .lk-button.lk-button-danger:hover {
    background: var(--destructive) !important;
    opacity: 0.9;
  }

  .lk-chat {
    background: var(--card) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-chat-entry {
    background: var(--muted) !important;
    color: var(--muted-foreground) !important;
    border-radius: calc(var(--radius) - 2px) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-chat-input {
    background: var(--input) !important;
    color: var(--foreground) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-chat-input:focus {
    border-color: var(--ring) !important;
    box-shadow: 0 0 0 2px var(--ring) !important;
  }

  .lk-settings-menu {
    background: var(--popover) !important;
    color: var(--popover-foreground) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-lg) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-toast {
    background: var(--card) !important;
    color: var(--card-foreground) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-lg) !important;
    font-family: var(--font-sans) !important;
  }

  /* Ensure VideoConference respects container boundaries */
  .lk-room-container .lk-video-conference {
    height: 100% !important;
    width: 100% !important;
    position: relative !important;
    max-height: none !important;
    max-width: none !important;
    font-family: var(--font-sans) !important;
  }

  /* Prevent LiveKit from setting viewport height */
  .lk-room-container .lk-video-conference > * {
    height: 100% !important;
    max-height: 100% !important;
  }

  /* Ensure grid layout works within our container */
  .lk-room-container .lk-grid-layout {
    height: 100% !important;
    width: 100% !important;
  }

  /* PopCircle specific enhancements */
  .lk-focus-layout {
    gap: 1rem !important;
  }

  .lk-grid-layout {
    gap: 0.75rem !important;
  }

  /* Custom scrollbar styling */
  .lk-chat::-webkit-scrollbar {
    width: 6px;
  }

  .lk-chat::-webkit-scrollbar-track {
    background: var(--muted);
    border-radius: var(--radius);
  }

  .lk-chat::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: var(--radius);
  }

  .lk-chat::-webkit-scrollbar-thumb:hover {
    background: var(--ring);
  }

  /* PreJoin component styling */
  .lk-prejoin {
    background: var(--background) !important;
    color: var(--foreground) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-prejoin .lk-button {
    background: var(--primary) !important;
    color: var(--primary-foreground) !important;
  }

  /* Connection quality indicator */
  .lk-connection-quality {
    border-radius: 50% !important;
  }

  .lk-connection-quality.lk-quality-excellent {
    background: var(--chart-2) !important;
  }

  .lk-connection-quality.lk-quality-good {
    background: var(--chart-5) !important;
  }

  .lk-connection-quality.lk-quality-poor {
    background: var(--destructive) !important;
  }

  /* Recording indicator with PopCircle styling */
  .lk-recording-indicator {
    background: var(--destructive) !important;
    color: var(--destructive-foreground) !important;
    border-radius: var(--radius) !important;
    font-family: var(--font-sans) !important;
    font-weight: 500 !important;
    box-shadow: var(--shadow-lg) !important;
  }

  /* Microphone and camera indicators */
  .lk-track-muted-indicator {
    background: var(--destructive) !important;
    color: var(--destructive-foreground) !important;
    border-radius: 50% !important;
  }

  /* Participant menu */
  .lk-participant-context-baseenu {
    background: var(--popover) !important;
    color: var(--popover-foreground) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-lg) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-participant-context-baseenu .lk-menu-item {
    color: var(--popover-foreground) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-participant-context-baseenu .lk-menu-item:hover {
    background: var(--accent) !important;
    color: var(--accent-foreground) !important;
  }

  /* Device selection menus */
  .lk-device-menu {
    background: var(--popover) !important;
    color: var(--popover-foreground) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-lg) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-device-menu-item {
    color: var(--popover-foreground) !important;
    font-family: var(--font-sans) !important;
  }

  .lk-device-menu-item:hover {
    background: var(--accent) !important;
    color: var(--accent-foreground) !important;
  }

  .lk-device-menu-item.lk-active {
    background: var(--primary) !important;
    color: var(--primary-foreground) !important;
  }
}

/* Custom utility classes for PopCircle theme */
@layer utilities {
  .popcircle-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
  }

  .popcircle-text-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .popcircle-shadow {
    box-shadow: var(--shadow-lg);
  }

  .popcircle-border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(var(--background), var(--background))
        padding-box,
      linear-gradient(135deg, var(--primary), var(--accent)) border-box;
  }

  /* hide-scrollbar.css */
  .hide-scrollbar {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
  @keyframes marquee {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  .animate-marquee {
    animation: marquee 20s linear infinite;
  }
}

:root .lk-control-bar {
  display: none !important;
}
