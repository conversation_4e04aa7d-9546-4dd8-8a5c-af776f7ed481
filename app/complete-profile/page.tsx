"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

const steps = [
  "My name is",
  "My Instagram username is",
  "Birthday",
  "Location",
  "Gender",
  "Photo",
  "Done",
];

export default function CompleteProfile() {
  const [step, setStep] = useState(0);
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    birthday: "",
    location: "",
    gender: "",
    photo: null as File | null,
  });

  const router = useRouter();

  useEffect(() => {
    if (step === 3 && !formData.location && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(async (position) => {
        const { latitude, longitude } = position.coords;
        try {
          const res = await fetch(
            `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${latitude}&lon=${longitude}`
          );
          const data = await res.json();
          const city =
            data.address.city ||
            data.address.town ||
            data.address.village ||
            "";
          const country = data.address.country || "";
          const fullLocation = `${city}${
            city && country ? ", " : ""
          }${country}`;
          setFormData((prev) => ({ ...prev, location: fullLocation }));
        } catch (err) {
          console.error("Location lookup failed", err);
        }
      });
    }
  }, [step, formData.location]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const calculateAge = (birthday: string) => {
    const birthDate = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const isStepComplete = () => {
    switch (step) {
      case 0:
        return formData.name.trim() !== "";
      case 1:
        return formData.username.trim() !== "";
      case 2:
        return (
          formData.birthday.trim() !== "" &&
          calculateAge(formData.birthday) >= 18
        );
      case 3:
        return formData.location.trim() !== "";
      case 4:
        return formData.gender.trim() !== "";
      case 5:
        return formData.photo !== null;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (isStepComplete() && step < steps.length - 1) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    if (step > 0) setStep(step - 1);
  };

  const handleFinish = async () => {
    const form = new FormData();
    form.append("name", formData.name);
    form.append("username", formData.username);
    form.append("birthday", formData.birthday);
    form.append("location", formData.location);
    form.append("gender", formData.gender);
    if (formData.photo) {
      form.append("photo", formData.photo);
    }

    const res = await fetch("/api/complete-profile", {
      method: "POST",
      body: form,
    });

    if (!res.ok) {
      console.error("Failed to submit profile:", await res.text());
      return;
    }

    router.push("/");
  };

  return (
    <div className="w-full h-screen flex flex-col items-center justify-center px-4">
      {/* Stepper Title + Loader */}
      <div className="w-full max-w-md mb-6">
        <h2 className="text-lg font-semibold mb-2 text-left">
          Build your profile
        </h2>
        <div className="flex justify-between w-full">
          {steps.map((label, index) => (
            <div key={label} className="flex-1 mx-1">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  index <= step ? "bg-blue-600" : "bg-gray-300"
                }`}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Step Prompt */}
      <div className="w-full max-w-md text-left">
        <h1 className="text-xl font-bold mb-6">{steps[step]}</h1>
      </div>

      {/* Inputs */}
      {step === 0 && (
        <input
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="Your name"
          className="input border-b p-2 w-full max-w-md mb-4"
        />
      )}

      {step === 1 && (
        <input
          name="username"
          value={formData.username}
          onChange={handleChange}
          placeholder="Instagram username"
          className="input border-b p-2 w-full max-w-md mb-4"
        />
      )}

      {step === 2 && (
        <>
          <input
            type="date"
            name="birthday"
            value={formData.birthday}
            onChange={handleChange}
            className="input border-b p-2 w-full max-w-md mb-2"
          />
          {formData.birthday && calculateAge(formData.birthday) < 18 && (
            <p className="text-red-600 text-sm">
              You must be at least 18 years old.
            </p>
          )}
        </>
      )}

      {step === 3 && (
        <input
          name="location"
          value={formData.location}
          onChange={handleChange}
          placeholder="Your location"
          className="input border-b p-2 w-full max-w-md mb-4"
        />
      )}

      {step === 4 && (
        <select
          name="gender"
          value={formData.gender}
          onChange={handleChange}
          className="input border-b p-2 w-full max-w-md mb-4"
        >
          <option value="">Select gender</option>
          <option value="man">Man</option>
          <option value="woman">Woman</option>
          <option value="non-binary">Non-binary</option>
        </select>
      )}

      {step === 5 && (
        <div className="w-full max-w-md mb-4">
          <input
            type="file"
            accept="image/*"
            onChange={(e) =>
              setFormData({ ...formData, photo: e.target.files?.[0] || null })
            }
            className="input border-b p-2 w-full mb-4"
          />
          {formData.photo && (
            <img
              src={URL.createObjectURL(formData.photo)}
              alt="Preview"
              className="w-24 h-24 rounded-full object-cover"
            />
          )}
        </div>
      )}

      {step === 6 && (
        <p className="w-full max-w-md text-left text-green-700 mb-4">
          🎉 You’re all set! Click finish to save your profile.
        </p>
      )}

      {/* Navigation Buttons */}
      <div className="flex justify-between w-full max-w-md mt-6">
        {step === 0 ? (
          <div className="text-sm invisible">Back</div>
        ) : (
          <button onClick={handleBack} className="text-sm">
            Back
          </button>
        )}
        {step < 6 && (
          <button
            onClick={handleNext}
            className={`text-sm ${
              !isStepComplete() ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={!isStepComplete()}
          >
            Next
          </button>
        )}
        {step === 6 && (
          <button
            onClick={handleFinish}
            className="bg-blue-600 text-white px-6 py-2 rounded text-sm"
          >
            Finish
          </button>
        )}
      </div>
    </div>
  );
}
