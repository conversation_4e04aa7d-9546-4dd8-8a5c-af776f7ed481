"use client";

import { useViewerToken } from "@/hooks/use-viewer-token";
import { LiveKitRoom, useParticipants } from "@livekit/components-react";
import { VideoConferenceWrapper } from "./video-conference-wrapper";
import { cn } from "@/lib/utils";
import { User, Stream } from "@prisma/client";
import ErrorBoundary from "@/components/error-boundary";
import { useEffect, useRef } from "react";
import { Room } from "livekit-client";
import { RecordingControls } from "@/components/stream-player/recording-controls.tsx"; // make sure path is correct

interface StreamPlayerProps {
  user: User & { stream: Stream | null };
  stream: Stream;
  isFollowing: boolean;
  enableMultiParticipant?: boolean;
}

const ParticipantsList = () => {
  const participants = useParticipants();

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold mb-2">Participants</h2>
      <ul className="space-y-1 text-sm">
        {participants.map((participant) => (
          <li key={participant.identity}>
            {participant.name || participant.identity}
            {participant.isSpeaking ? " 🔊" : ""}
            {participant.isLocal ? " (You)" : ""}
          </li>
        ))}
      </ul>
    </div>
  );
};

export const StreamPlayer = ({
  user,
  enableMultiParticipant = true,
}: StreamPlayerProps) => {
  const { token, name, identity, isLoading, error } = useViewerToken(
    user.username
  );
  const roomRef = useRef<Room | null>(null);

  useEffect(() => {
    return () => {
      if (roomRef.current) {
        roomRef.current.disconnect();
        roomRef.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-base font-semibold">Loading stream...</p>
          <p className="text-muted-foreground mt-2">Connecting to the stream</p>
        </div>
      </div>
    );
  }

  if (error || !token || !name || !identity) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-base font-semibold">Cannot watch the stream</p>
          <p className="text-muted-foreground mt-2">
            {error || "Failed to connect to the stream"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <LiveKitRoom
        token={token}
        serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_WS_URL}
        roomName="global-dashboard-room"
        audio={true} // auto-on audio
        video={true} // auto-on video
        connect={true}
        onConnected={() => console.log("Connected to global dashboard room")}
        onDisconnected={() => console.log("Disconnected")}
        className={cn("h-full")}
      >
        <div className="w-full h-full flex">
          <div className="flex-1">
            <VideoConferenceWrapper hostIdentity={user.externalUserId} />
          </div>
          <div className="w-64 border-l border-border bg-muted overflow-y-auto flex flex-col">
            <ParticipantsList />
            <div className="p-4">
              <RecordingControls hostUserId={user.externalUserId} />
            </div>
          </div>
        </div>
      </LiveKitRoom>
    </ErrorBoundary>
  );
};
