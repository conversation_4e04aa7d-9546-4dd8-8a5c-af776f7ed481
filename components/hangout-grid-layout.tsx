"use client";

import { useTracks, ParticipantTile, TrackReference } from "@livekit/components-react";
import { Track } from "livekit-client";
import { cn } from "@/lib/utils";

interface HangoutGridLayoutProps {
  className?: string;
}

// Custom participant tile with 9:16 aspect ratio
const HangoutParticipantTile = ({ trackRef }: { trackRef: TrackReference }) => {
  return (
    <div className="relative bg-gray-900 border border-gray-700 rounded-lg overflow-hidden aspect-[9/16]">
      <ParticipantTile 
        trackRef={trackRef}
        className="w-full h-full"
      />
    </div>
  );
};

export const HangoutGridLayout = ({ className }: HangoutGridLayoutProps) => {
  // Get all video tracks for the grid
  const tracks = useTracks([
    Track.Source.Camera,
    Track.Source.ScreenShare,
  ]);

  // Create a grid layout based on participant count
  const getGridClass = (count: number) => {
    if (count === 1) return "grid-cols-1";
    if (count === 2) return "grid-cols-2";
    if (count <= 4) return "grid-cols-2";
    if (count <= 6) return "grid-cols-3";
    return "grid-cols-3";
  };

  return (
    <div className={cn("h-full p-4", className)}>
      <div className={cn(
        "grid gap-4 h-full",
        getGridClass(tracks.length)
      )}>
        {tracks.map((trackRef) => (
          <HangoutParticipantTile 
            key={`${trackRef.participant.identity}-${trackRef.source}`}
            trackRef={trackRef}
          />
        ))}
      </div>
    </div>
  );
};
