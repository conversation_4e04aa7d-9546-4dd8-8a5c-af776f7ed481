"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import {
    Mic,
    MicOff,
    Video,
    VideoOff,
    Phone,
    MoreVertical,
    Volume2,
    VolumeX
} from "lucide-react";
import { User } from "@prisma/client";

interface HangoutParticipant {
    id: string;
    user: User;
    isAudioEnabled: boolean;
    isVideoEnabled: boolean;
    isSpeaking: boolean;
    isHost: boolean;
}

interface HangoutRoomProps {
    roomName: string;
    host: User;
    currentUser: User;
    participants: HangoutParticipant[];
    onLeave: () => void;
}

export const HangoutRoom = ({
    roomName,
    host,
    currentUser,
    participants,
    onLeave,
}: HangoutRoomProps) => {
    const [isAudioEnabled, setIsAudioEnabled] = useState(true);
    const [isVideoEnabled, setIsVideoEnabled] = useState(true);
    const [isMuted, setIsMuted] = useState(false);

    const toggleAudio = useCallback(() => {
        setIsAudioEnabled(prev => !prev);
    }, []);

    const toggleVideo = useCallback(() => {
        setIsVideoEnabled(prev => !prev);
    }, []);

    const toggleMute = useCallback(() => {
        setIsMuted(prev => !prev);
    }, []);

    // Create a grid layout based on participant count
    const getGridClass = (count: number) => {
        if (count === 1) return "grid-cols-1";
        if (count === 2) return "grid-cols-2";
        if (count <= 4) return "grid-cols-2";
        if (count <= 6) return "grid-cols-3";
        return "grid-cols-3";
    };

    const ParticipantTile = ({ participant }: { participant: HangoutParticipant }) => (
        <Card className="relative bg-gray-900 border-gray-700 overflow-hidden group">
            <CardContent className="p-0 aspect-[9/16] relative">
                {/* Video/Avatar area */}
                <div className="absolute inset-0 bg-gradient-to-b from-gray-800 to-gray-900 flex items-center justify-center">
                    {participant.isVideoEnabled ? (
                        <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                            <span className="text-gray-400 text-sm">Video Stream</span>
                        </div>
                    ) : (
                        <Avatar className="w-20 h-20">
                            <AvatarImage src={participant.user.imageURL} />
                            <AvatarFallback className="text-2xl bg-gray-700 text-white">
                                {participant.user.username.charAt(0).toUpperCase()}
                            </AvatarFallback>
                        </Avatar>
                    )}
                </div>

                {/* Participant info overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <span className="text-white font-medium text-sm truncate">
                                {participant.user.username}
                                {participant.id === currentUser.id && " (You)"}
                            </span>
                            {participant.isHost && (
                                <span className="text-xs px-1.5 py-0.5 bg-gray-600 text-white rounded">
                                    Host
                                </span>
                            )}
                        </div>
                        <div className="flex items-center space-x-1">
                            {!participant.isAudioEnabled && (
                                <div className="p-1 bg-red-500 rounded-full">
                                    <MicOff className="w-3 h-3 text-white" />
                                </div>
                            )}
                            {participant.isSpeaking && participant.isAudioEnabled && (
                                <div className="p-1 bg-green-500 rounded-full animate-pulse">
                                    <Mic className="w-3 h-3 text-white" />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const joinButtonArea = participants.length < 8 && (
        <Card className="relative bg-gray-900/30 border-gray-700 border-dashed overflow-hidden">
            <CardContent className="p-0 aspect-[9/16] relative">
                <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-400">
                    <div className="w-16 h-16 rounded-full border-2 border-gray-500 border-dashed flex items-center justify-center mb-3">
                        <span className="text-2xl">+</span>
                    </div>
                    <span className="text-sm font-medium">Join below to begin a hangout</span>
                    <span className="text-xs mt-1">Stream and record</span>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <div className="flex flex-col h-screen bg-black text-white">
            {/* Header */}
            <div className="flex items-center justify-between p-4 bg-gray-900/50 backdrop-blur">
                <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                        <AvatarImage src={host.imageURL} />
                        <AvatarFallback>{host.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                        <h1 className="font-semibold">{roomName}</h1>
                        <p className="text-sm text-gray-400">{participants.length} people here</p>
                    </div>
                </div>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onLeave}
                    className="text-white hover:bg-gray-800"
                >
                    Leave
                </Button>
            </div>

            {/* Main video grid */}
            <div className="flex-1 p-4 overflow-hidden">
                <div className={`grid ${getGridClass(participants.length + 1)} gap-4 h-full`}>
                    {participants.map((participant) => (
                        <ParticipantTile key={participant.id} participant={participant} />
                    ))}
                    {joinButtonArea}
                </div>
            </div>

            {/* Controls */}
            <div className="flex items-center justify-center space-x-4 p-6 bg-gray-900/50 backdrop-blur">
                <Button
                    variant={isAudioEnabled ? "default" : "destructive"}
                    size="lg"
                    onClick={toggleAudio}
                    className="rounded-full w-14 h-14"
                >
                    {isAudioEnabled ? <Mic className="w-6 h-6" /> : <MicOff className="w-6 h-6" />}
                </Button>

                <Button
                    variant={isVideoEnabled ? "default" : "destructive"}
                    size="lg"
                    onClick={toggleVideo}
                    className="rounded-full w-14 h-14"
                >
                    {isVideoEnabled ? <Video className="w-6 h-6" /> : <VideoOff className="w-6 h-6" />}
                </Button>

                <Button
                    variant="destructive"
                    size="lg"
                    onClick={onLeave}
                    className="rounded-full w-14 h-14"
                >
                    <Phone className="w-6 h-6" />
                </Button>

                <Button
                    variant={isMuted ? "destructive" : "default"}
                    size="lg"
                    onClick={toggleMute}
                    className="rounded-full w-14 h-14"
                >
                    {isMuted ? <VolumeX className="w-6 h-6" /> : <Volume2 className="w-6 h-6" />}
                </Button>

                <Button
                    variant="ghost"
                    size="lg"
                    className="rounded-full w-14 h-14 text-white hover:bg-gray-800"
                >
                    <MoreVertical className="w-6 h-6" />
                </Button>
            </div>
        </div>
    );
}; 