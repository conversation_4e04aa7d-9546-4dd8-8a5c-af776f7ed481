"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "@prisma/client";
import {
    LiveKitRoom,
    useParticipants,
    RoomAudioRenderer,
    ControlBar
} from "@livekit/components-react";
import { useHangoutToken } from "@/hooks/use-hangout-token";
import { HangoutGridLayout } from "./hangout-grid-layout";

interface HangoutRoomProps {
    roomName: string;
    host: User;
    hangoutId: string;
}



// Inner component that uses LiveKit hooks
const HangoutRoomContent = ({
    roomName,
    host,
}: Omit<HangoutRoomProps, 'hangoutId'>) => {
    const router = useRouter();
    const participants = useParticipants();

    const handleLeave = useCallback(() => {
        router.push('/hangouts');
    }, [router]);

    return (
        <div className="flex flex-col h-screen bg-black text-white">
            {/* Header */}
            <div className="flex items-center justify-between p-4 bg-gray-900/50 backdrop-blur">
                <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                        <AvatarImage src={host.imageURL} />
                        <AvatarFallback>{host.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                        <h1 className="font-semibold">{roomName}</h1>
                        <p className="text-sm text-gray-400">{participants.length} people here</p>
                    </div>
                </div>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLeave}
                    className="text-white hover:bg-gray-800"
                >
                    Leave
                </Button>
            </div>

            {/* Main video grid with 9:16 aspect ratio tiles */}
            <div className="flex-1 overflow-hidden">
                <HangoutGridLayout />
            </div>

            {/* Controls - Available to all participants */}
            <div className="flex items-center justify-center space-x-4 p-6 bg-gray-900/50 backdrop-blur">
                <ControlBar
                    variation="minimal"
                    controls={{
                        microphone: true,
                        camera: true,
                        screenShare: true,
                        leave: true,
                    }}
                />
            </div>

            {/* Audio renderer for all participants */}
            <RoomAudioRenderer />
        </div>
    );
};

// Main component wrapper with LiveKit room
export const HangoutRoom = ({
    roomName,
    host,
    hangoutId,
}: HangoutRoomProps) => {
    const { token, name, identity } = useHangoutToken(hangoutId);

    if (!token || !name || !identity) {
        return (
            <div className="flex items-center justify-center h-screen bg-black text-white">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                    <p>Connecting to hangout...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-screen" data-lk-theme="default">
            <LiveKitRoom
                token={token}
                serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_WS_URL!}
                connect={true}
                audio={true}
                video={true}
                className="h-full"
                room={`hangout-${hangoutId}`}
            >
                <HangoutRoomContent
                    roomName={roomName}
                    host={host}
                />
            </LiveKitRoom>
        </div>
    );
};